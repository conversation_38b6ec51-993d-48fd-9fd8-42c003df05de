<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClipPie Settings</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="icon">📋</div>
            <h1>ClipPie</h1>
            <p class="subtitle">Manage your clipboard history settings</p>
        </div>

        <!-- Settings Form -->
        <form id="settingsForm" class="settings-form">
            <!-- History Limit Setting -->
            <div class="setting-group">
                <label for="historyLimit" class="setting-label">
                    <span class="label-text">Clipboard History Limit</span>
                    <span class="label-description">Maximum number of items to store</span>
                </label>
                <div class="input-wrapper">
                    <input 
                        type="number" 
                        id="historyLimit" 
                        name="historyLimit" 
                        min="5" 
                        max="100" 
                        value="25"
                        class="number-input"
                    >
                    <span class="input-suffix">items</span>
                </div>
            </div>

            <!-- Keyboard Shortcut Display -->
            <div class="setting-group">
                <label class="setting-label">
                    <span class="label-text">Keyboard Shortcut</span>
                    <span class="label-description">Press to open clipboard overlay</span>
                </label>
                <div class="shortcut-display">
                    <kbd class="key">Ctrl</kbd>
                    <span class="plus">+</span>
                    <kbd class="key">Shift</kbd>
                    <span class="plus">+</span>
                    <kbd class="key">X</kbd>
                </div>
                <p class="shortcut-note">
                    To change this shortcut, go to Chrome Extensions → Keyboard shortcuts
                </p>
            </div>

            <!-- Current Statistics -->
            <div class="setting-group">
                <label class="setting-label">
                    <span class="label-text">Current Statistics</span>
                </label>
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-value" id="currentCount">0</span>
                        <span class="stat-label">Items stored</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="storageUsed">0</span>
                        <span class="stat-label">KB used</span>
                    </div>
                </div>
            </div>

            <!-- Manual Clipboard Capture -->
            <div class="setting-group">
                <label class="setting-label">
                    <span class="label-text">Manual Clipboard Capture</span>
                    <span class="label-description">Capture current clipboard content (useful for Mac address bar)</span>
                </label>
                <button type="button" id="captureClipboardBtn" class="btn btn-capture">
                    <span class="btn-text">📋 Capture Clipboard</span>
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="button-group">
                <button type="submit" class="btn btn-primary" id="saveBtn">
                    <span class="btn-text">Save Settings</span>
                </button>
                <button type="button" class="btn btn-secondary" id="clearBtn">
                    <span class="btn-text">Clear History</span>
                </button>
            </div>
        </form>

        <!-- Status Messages -->
        <div id="statusMessage" class="status-message hidden"></div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
