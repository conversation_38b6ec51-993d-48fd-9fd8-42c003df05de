<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClipPie Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-steps {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .copy-text {
            background: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 ClipPie Extension Test Page</h1>
    
    <div class="instructions">
        <h2>📋 Instructions</h2>
        <p>This page is designed to test the ClipPie Chrome extension functionality. Make sure the extension is loaded and enabled before testing.</p>
        <p><strong>Keyboard Shortcut:</strong> Press <kbd>Ctrl+Shift+X</kbd> to open the ClipPie overlay.</p>
    </div>

    <div class="test-section">
        <h2>🎯 Test 1: Tag Clicking Paste Location</h2>
        <div class="test-steps">
            <ol>
                <li>Click in the text input below to focus it</li>
                <li>Type some text and position your cursor in the middle</li>
                <li>Press <kbd>Ctrl+Shift+X</kbd> to open ClipPie overlay</li>
                <li>Click any tag in the overlay</li>
                <li>Verify the content pastes at your cursor position (not at the end)</li>
            </ol>
        </div>
        <input type="text" placeholder="Click here, type some text, position cursor, then open overlay and click a tag" id="test1-input">
        <textarea placeholder="You can also test with this textarea. Click here, type text, position cursor in the middle, then open overlay and click a tag." id="test1-textarea"></textarea>
    </div>

    <div class="test-section">
        <h2>⌨️ Test 2: Ctrl+C Clipboard Capture</h2>
        <div class="test-steps">
            <ol>
                <li>Select and copy the text below using <kbd>Ctrl+C</kbd></li>
                <li>Open ClipPie overlay with <kbd>Ctrl+Shift+X</kbd></li>
                <li>Verify the copied text appears as a new tag</li>
                <li>Try copying different text and see if it updates automatically</li>
            </ol>
        </div>
        <div class="copy-text">
            Test text for Ctrl+C: Hello from ClipPie test! This should be captured when you copy it.
        </div>
        <div class="copy-text">
            Another test text: ClipPie clipboard monitoring works great! Copy this too.
        </div>
        <div class="copy-text">
            Third test: Multiple clipboard items should all appear in the overlay.
        </div>
    </div>

    <div class="test-section">
        <h2>🔄 Test 3: Multiple Paste Operations</h2>
        <div class="test-steps">
            <ol>
                <li>Focus on the input below</li>
                <li>Open ClipPie overlay</li>
                <li>Click multiple tags to paste different content</li>
                <li>Verify each paste goes to the correct location</li>
                <li>Test that overlay stays open for multiple operations</li>
            </ol>
        </div>
        <input type="text" placeholder="Test multiple paste operations here" id="test3-input">
        <textarea placeholder="Or test multiple pastes in this textarea" id="test3-textarea"></textarea>
    </div>

    <div class="test-section">
        <h2>🎨 Test 4: Different Input Types</h2>
        <div class="test-steps">
            <ol>
                <li>Test pasting in different types of input fields</li>
                <li>Verify the extension works with all editable elements</li>
            </ol>
        </div>
        <input type="text" placeholder="Regular text input" id="test4-text">
        <input type="email" placeholder="Email input" id="test4-email">
        <input type="password" placeholder="Password input" id="test4-password">
        <input type="search" placeholder="Search input" id="test4-search">
        <div contenteditable="true" style="border: 1px solid #ccc; padding: 10px; min-height: 50px; background: white;">
            Contenteditable div - click here and test pasting
        </div>
    </div>

    <div class="test-section">
        <h2>🐛 Debug Information</h2>
        <p>Open the browser's Developer Tools (F12) and check the Console tab for debug messages while testing. Look for:</p>
        <ul>
            <li>✓ Focus tracking messages when clicking in inputs</li>
            <li>📋 Clipboard capture messages when copying</li>
            <li>🎯 Paste target information when clicking tags</li>
            <li>❌ Any error messages</li>
        </ul>
    </div>

    <script>
        // Add some basic interaction logging for debugging
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 ClipPie test page loaded');
            
            // Log focus events for debugging
            document.addEventListener('focusin', (e) => {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.contentEditable === 'true') {
                    console.log('🎯 Test page: Focus on', e.target.tagName, e.target.type, e.target.id || e.target.className);
                }
            });
            
            // Log copy events
            document.addEventListener('copy', () => {
                console.log('📋 Test page: Copy event detected');
            });
        });
    </script>
</body>
</html>
