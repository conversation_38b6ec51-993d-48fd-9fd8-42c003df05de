# 📋 ClipPie - Smart Clipboard Manager

<div align="center">

![Clip<PERSON><PERSON> Logo](icons/icon128.png)

**A beautiful, modern Chrome extension that transforms your clipboard into an intelligent, visual workspace.**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Chrome Web Store](https://img.shields.io/badge/Chrome-Extension-blue.svg)](https://chrome.google.com/webstore)
[![Version](https://img.shields.io/badge/version-1.0.0-green.svg)](https://github.com/ernstrmlo/ClipPie/releases)

[🚀 Install from Chrome Web Store](#installation) • [📖 Documentation](#usage) • [🤝 Contributing](CONTRIBUTING.md) • [🐛 Report Issues](https://github.com/ernstrmlo/ClipPie/issues)

</div>

---

## ✨ What is ClipPie?

ClipPie revolutionizes how you manage copied content in Chrome. Instead of losing track of what you've copied, <PERSON><PERSON><PERSON><PERSON> automatically captures your clipboard history and presents it in a beautiful, interactive tag cloud interface. Copy once, paste anywhere, anytime!

## 🎯 Key Features

<table>
<tr>
<td width="50%">

### 🔄 **Smart Clipboard Capture**
- 🤖 **Automatic monitoring** of copied text in Chrome
- 🍎 **Enhanced Mac support** for address bar copying
- 🎫 **Zendesk optimization** for seamless text editor integration
- 💾 **Local storage** using Chrome's secure storage API
- 🔢 **Configurable history** (5-100 items)
- 🔄 **FIFO management** when limit is reached
- 📋 **Manual capture** button for special scenarios

### 🎨 **Beautiful Tag Cloud**
- 🏷️ **Visual tag cloud** display of clipboard items
- 🌊 **Organic, flowing** layout for natural feel
- ✨ **Smooth animations** and transitions
- 📱 **Responsive design** for all screen sizes

</td>
<td width="50%">

### 📝 **Intelligent Pasting**
- 👆 **Click-to-Paste**: Instant pasting at cursor location
- 🖱️ **Drag-to-Paste**: Drag tags to any input field
- 🎯 **Smart detection** of editable elements
- 📋 **Fallback clipboard** writing when needed

### 🎯 **Hover Sticky Notes**
- 🏷️ **Hover preview** with colorful sticky notes
- 🌈 **Multiple pastel colors** (yellow, pink, blue, green)
- 🎭 **Smooth animations** and smart positioning
- 👁️ **Non-intrusive** design philosophy

</td>
</tr>
</table>

### ⚙️ **Powerful Settings**
- 🎛️ Set clipboard history limit (5-100 items)
- 📊 View real-time statistics (item count, storage usage)
- 🗑️ Clear history with confirmation dialog
- 🎨 Modern, clean settings interface

### ⌨️ **Keyboard Shortcuts**
- **`Ctrl+Shift+X`** (⌘+Shift+X on Mac) to toggle overlay
- **`Escape`** to close overlay
- 🔧 Customizable through Chrome's extension shortcuts

## 🚀 Installation

### Option 1: Chrome Web Store (Recommended)
*Coming soon! The extension is currently under review.*

### Option 2: Developer Installation

1. **Download** the latest release from [GitHub Releases](https://github.com/ernstrmlo/ClipPie/releases)
2. **Extract** the ZIP file to a folder
3. **Open Chrome** and navigate to `chrome://extensions/`
4. **Enable** "Developer mode" in the top right corner
5. **Click** "Load unpacked" and select the extracted folder
6. **Pin** the ClipPie icon to your toolbar for easy access

## 📖 Usage

### Getting Started
1. **📋 Copy text** anywhere in Chrome (`Ctrl+C` or right-click → copy)
2. **⌨️ Open overlay** by pressing `Ctrl+Shift+X`
3. **👀 View items** in the beautiful tag cloud interface
4. **🖱️ Hover** over tags to see full content in colorful sticky notes
5. **👆 Click** tags to paste instantly, or **drag** them to input fields
6. **⚙️ Configure** settings by clicking the ClipPie extension icon

### Pro Tips
- 🎯 **Drag and drop** tags directly onto input fields for precise pasting
- 🔄 **Auto-refresh** - the overlay updates automatically when you copy new content
- 📏 **Resizable window** - drag the corners to resize the overlay to your preference
- 🎨 **Color coding** - each clipboard item gets a unique pastel color for easy identification
- 🍎 **Mac users** - Use the manual capture button for address bar URLs
- 🎫 **Zendesk users** - Optimized text insertion prevents content corruption

### 🎫 Zendesk Optimization
ClipPie is specially optimized for Zendesk environments:
- **Smart Editor Detection** - Automatically detects Zendesk text editors
- **Enhanced Text Insertion** - Uses `execCommand` for better compatibility
- **Event Simulation** - Triggers proper input events to prevent content corruption
- **Rich Text Support** - Maintains text formatting in Zendesk editors

## 📁 Project Structure

```
ClipPie/
├── 📄 manifest.json          # Extension configuration (Manifest V3)
├── ⚙️ background.js          # Service worker for clipboard monitoring
├── 🎨 popup.html            # Settings interface HTML
├── 💅 popup.css             # Settings interface styles
├── 🔧 popup.js              # Settings interface logic
├── 📝 content.js            # Content script for overlay functionality
├── 🎭 overlay.css           # Overlay styles and animations
├── 🖼️ icons/                # Extension icons (16, 32, 48, 128px)
├── 📚 docs/                 # Documentation files
├── 📋 CONTRIBUTING.md       # Contribution guidelines
├── ⚖️ LICENSE              # MIT License
└── 📖 README.md             # This file
```

## Technical Details

### Architecture
- **Manifest V3** compliance with service worker
- **Content Script** injection for overlay functionality
- **Chrome Storage API** for local data persistence
- **Chrome Commands API** for keyboard shortcuts
- **Chrome Clipboard API** for reading clipboard content

### Security & Privacy
- All data stored locally on user's device
- No external network requests
- No data collection or tracking
- Clipboard access only when extension has focus (Chrome security requirement)

### Browser Compatibility
- Chrome 88+ (Manifest V3 requirement)
- Chromium-based browsers (Edge, Brave, etc.)

## Limitations

- **Image Support**: Currently only supports text content. Image URLs can be captured if copied as text, but raw image data is not supported due to Chrome's clipboard API limitations.
- **Clipboard Monitoring**: Due to security restrictions, clipboard reading only works when the extension has focus. This is a Chrome limitation, not an extension bug.
- **Cross-Origin**: Some websites with strict Content Security Policies may limit functionality.

## Development

### Key Components

1. **Background Script** (`background.js`)
   - Monitors clipboard using `navigator.clipboard.readText()`
   - Manages storage with FIFO queue
   - Handles keyboard command listeners
   - Injects content scripts on demand

2. **Content Script** (`content.js`)
   - Creates and manages overlay DOM
   - Handles tag cloud population
   - Manages drag-and-drop interactions
   - Implements smart pasting logic

3. **Settings Interface** (`popup.html/css/js`)
   - Clean, modern settings panel
   - Real-time statistics display
   - Form validation and error handling
   - Responsive design

### Styling Philosophy
- **Color Palette**: Clean whites, soft greys, and pastel accents
- **Typography**: Inter font family for modern, readable text
- **Animations**: Smooth CSS transitions for professional feel
- **Layout**: Flexbox and CSS Grid for responsive design

## 🚀 Roadmap

<details>
<summary><strong>🔮 Planned Features</strong></summary>

- [ ] 🖼️ **Image clipboard support** (when Chrome APIs allow)
- [ ] 📝 **Rich text formatting** preservation
- [ ] 🔍 **Search/filter** functionality for large histories
- [ ] 🏷️ **Categories and custom tags** for organization
- [ ] 📤 **Export/import** clipboard history
- [ ] ☁️ **Sync across devices** (optional cloud sync)
- [ ] ⌨️ **Custom keyboard shortcuts** configuration
- [ ] 🌙 **Dark mode theme** support
- [ ] 📊 **Usage analytics** and insights
- [ ] 🔒 **Encrypted storage** for sensitive content

</details>

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Quick Start for Contributors
1. 🍴 Fork the repository
2. 🌿 Create a feature branch (`git checkout -b feature/amazing-feature`)
3. ✨ Make your changes
4. 🧪 Test thoroughly
5. 📝 Commit your changes (`git commit -m 'Add amazing feature'`)
6. 🚀 Push to the branch (`git push origin feature/amazing-feature`)
7. 🎯 Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Community

- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/ernstrmlo/ClipPie/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/ernstrmlo/ClipPie/discussions)
- 📧 **Contact**: <EMAIL>
- ⭐ **Star this repo** if you find it helpful!

---

<div align="center">

**Made with ❤️ by [Ernst Romelo](https://github.com/ernstrmlo)**

*If ClipPie helps improve your workflow, consider giving it a ⭐ on GitHub!*

</div>
