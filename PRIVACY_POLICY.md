# Privacy Policy for ClipPie

**Last Updated: December 2024**

## Overview

ClipPie is committed to protecting your privacy. This Privacy Policy explains how ClipPie handles your information when you use our Chrome extension.

## Information We Collect

**ClipPie collects NO personal information whatsoever.**

### What We Don't Collect:
- Personal identification information
- Browsing history
- Website data
- User analytics
- Usage statistics
- Any data that could identify you

### What We Do Store Locally:
- **Clipboard Text Content**: Only the text you copy is stored locally on your device
- **Extension Settings**: Your preferences (like history limit) are stored locally
- **No External Storage**: Nothing is ever sent to external servers

## How We Use Information

ClipPie operates entirely on your local device:

- **Local Storage Only**: All clipboard history is stored in your browser's local storage
- **No Data Transmission**: No data is ever sent to external servers
- **No Analytics**: We don't track how you use the extension
- **No Advertising**: We don't collect data for advertising purposes

## Data Storage and Security

### Local Storage:
- All data is stored using Chrome's secure local storage API
- Data never leaves your device
- You have complete control over your data
- Data is automatically cleared when you uninstall the extension

### Security Measures:
- No network connections are made by the extension
- No external APIs are accessed
- No third-party services are used
- Chrome's built-in security protects your local data

## Data Sharing

**We do not share any data because we do not collect any data.**

- No data is shared with third parties
- No data is sold or monetized
- No data is transmitted anywhere
- Your clipboard content remains completely private

## Your Rights and Control

You have complete control over your data:

- **View Data**: Access your clipboard history through the extension interface
- **Delete Data**: Clear your history anytime through the extension settings
- **Export Data**: Your data is stored locally and accessible to you
- **Complete Removal**: Uninstalling the extension removes all stored data

## Permissions Explanation

ClipPie requests minimal permissions:

- **storage**: To save your clipboard history locally on your device
- **clipboardRead**: To read clipboard content when you copy text
- **clipboardWrite**: To paste content to input fields
- **activeTab**: To inject the overlay interface on web pages
- **scripting**: To provide the visual overlay functionality
- **commands**: To handle keyboard shortcuts (Ctrl+Shift+X)

**Important**: These permissions are used solely for the extension's functionality and never for data collection.

## Children's Privacy

ClipPie does not collect any personal information from anyone, including children under 13. Since no data is collected, there are no special considerations for children's privacy beyond the general privacy protections described above.

## Changes to This Policy

If we make changes to this privacy policy, we will update the "Last Updated" date above. Since ClipPie doesn't collect any data, any changes would likely be clarifications or updates to reflect new features that maintain our privacy-first approach.

## Open Source Transparency

ClipPie is open source software. You can review the complete source code at:
https://github.com/ernstrmlo/ClipPie

This transparency allows you to verify that:
- No data collection occurs
- No external connections are made
- The extension works exactly as described

## Contact Information

If you have any questions about this Privacy Policy or ClipPie's privacy practices, please contact:

**Email**: <EMAIL>
**GitHub**: https://github.com/ernstrmlo/ClipPie

## Summary

**ClipPie is designed with privacy as a core principle:**
- ✅ Zero data collection
- ✅ 100% local storage
- ✅ No external connections
- ✅ No tracking or analytics
- ✅ Open source transparency
- ✅ Complete user control

Your privacy is not just protected—it's guaranteed by design.
